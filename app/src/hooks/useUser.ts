/**
 * Custom hooks for user management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UserService } from '@/services/api/userService';
import {
  UserCreateRequest,
  UserUpdateRequest,
  LoginRequest
} from '@/types/api/user';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching all users
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with users data, loading state, and error state
 */
export const useUsers = (organizationId: number, params?: QueryParams) => {
  return useQuery({
    queryKey: ['users', organizationId, params],
    queryFn: () => UserService.getUsers(organizationId, params),
  });
};

/**
 * Hook for fetching a single user by ID
 * @param id User ID
 * @returns Query result with user data, loading state, and error state
 */
export const useUser = (id: number, organizationId: number) => {
  return useQuery({
    queryKey: ['user', id, organizationId],
    queryFn: () => UserService.getUserById(id, organizationId),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

/**
 * Hook for creating a new user
 * @returns Mutation function and state for creating a user
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UserCreateRequest) => UserService.createUser(data),
    onSuccess: () => {
      // Invalidate the users query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for updating an existing user
 * @returns Mutation function and state for updating a user
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UserUpdateRequest }) =>
      UserService.updateUser(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific user and users list queries
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for deleting a user
 * @returns Mutation function and state for deleting a user
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      UserService.deleteUser(id, organizationId),
    onSuccess: () => {
      // Invalidate the users query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for enabling a user
 * @returns Mutation function and state for enabling a user
 */
export const useEnableUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      UserService.enableUser(id, organizationId),
    onSuccess: (_, id) => {
      // Invalidate specific user and users list queries
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for disabling a user
 * @returns Mutation function and state for disabling a user
 */
export const useDisableUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      UserService.disableUser(id, organizationId),
    onSuccess: (_, id) => {
      // Invalidate specific user and users list queries
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for searching users
 * @param searchTerm Search term
 * @param role Role filter
 * @param enabled Enabled filter
 * @param params Additional query parameters
 * @returns Query result with search results
 */
export const useSearchUsers = (
  searchTerm?: string,
  role?: string,
  enabled?: boolean,
  params?: QueryParams
) => {
  return useQuery({
    queryKey: ['users', 'search', searchTerm, role, enabled, params],
    queryFn: () => UserService.searchUsers(searchTerm, role, enabled, params),
    enabled: !!(searchTerm || role !== undefined || enabled !== undefined),
  });
};

// Legacy authentication hooks for backward compatibility
export const useLogin = () => {
  return useMutation({
    mutationFn: (data: LoginRequest) => UserService.login(data),
    onSuccess: (data) => {
      localStorage.setItem('auth_token', data.data.token);
    },
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: UserService.logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });
};