/**
 * Custom hooks for designation management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DesignationService } from '@/services/api/designationService';
import {
  CreateDesignationRequest,
  UpdateDesignationRequest
} from '@/types/designation.types';
import { QueryParams } from '@/types/api/common';

// Default organization ID for testing/development
const DEFAULT_ORGANIZATION_ID = 40928446087168;

/**
 * Hook for fetching a paginated list of designations
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with designations data, loading state, and error state
 */
export const useDesignations = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['designations', params],
    queryFn: () => DesignationService.getDesignations(params),
  });
};

/**
 * Hook for fetching a single designation by ID
 * @param id Designation ID
 * @param organizationId Organization ID
 * @returns Query result with designation data, loading state, and error state
 */
export const useDesignation = (id: string, organizationId: number) => {
  return useQuery({
    queryKey: ['designation', id, organizationId],
    queryFn: () => DesignationService.getDesignationById(id, organizationId),
    enabled: !!id && !!organizationId,
  });
};

/**
 * Hook for creating a new designation
 * @returns Mutation function and state for creating a designation
 */
export const useCreateDesignation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) =>
      DesignationService.createDesignation(data, DEFAULT_ORGANIZATION_ID),
    onSuccess: () => {
      // Invalidate the designations query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['designations'] });
    },
  });
};

/**
 * Hook for updating an existing designation
 * @returns Mutation function and state for updating a designation
 */
export const useUpdateDesignation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data, organizationId }: { id: string; data: any; organizationId: number }) =>
      DesignationService.updateDesignation(id, data, organizationId),
    onSuccess: (_, variables) => {
      // Invalidate the specific designation query and the designations list
      queryClient.invalidateQueries({ queryKey: ['designation', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['designations'] });
    },
  });
};

/**
 * Hook for deleting a designation
 * @returns Mutation function and state for deleting a designation
 */
export const useDeleteDesignation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: string; organizationId: number }) =>
      DesignationService.deleteDesignation(id, organizationId),
    onSuccess: () => {
      // Invalidate the designations query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['designations'] });
    },
  });
};
