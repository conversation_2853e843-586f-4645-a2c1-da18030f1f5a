import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import userFormSchemaJson from '@/formSchemas/userForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCreateUser } from '@/hooks/useUser';
import { useDepartments } from '@/hooks/useDepartment';
import { useDesignations } from '@/hooks/useDesignation';
import { UserCreateRequest, Role } from '@/types/api/user';
import { ROUTES } from '@/constants/routes.constant';
import './AddUser.css';

const AddUser: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // State for form schema
  const [userFormSchema, setUserFormSchema] = useState<FormSchema>(userFormSchemaJson as FormSchema);

  // Fetch departments and designations for dropdowns
  const organizationId = **************; // Use specified organization ID
  const { data: departmentsData } = useDepartments(organizationId);
  const { data: designationsData } = useDesignations();

  const departments = departmentsData || [];
  const designations = designationsData?.data || [];

  // Create user mutation
  const createUserMutation = useCreateUser();

  // Update form schema with department and designation options
  useEffect(() => {
    const updatedSchema = { ...userFormSchema };

    // Update department options
    const departmentField = updatedSchema.fields.find(field => field.name === 'departmentId');
    if (departmentField && departmentField.type === 'select') {
      departmentField.options = departments.map(dept => ({
        label: dept.name,
        value: dept.id?.toString() || ''
      }));
    }

    // Update designation options
    const designationField = updatedSchema.fields.find(field => field.name === 'designationId');
    if (designationField && designationField.type === 'select') {
      designationField.options = designations.map(designation => ({
        label: designation.name,
        value: designation.id.toString()
      }));
    }

    setUserFormSchema(updatedSchema);
  }, [departments, designations]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const userData: UserCreateRequest = {
        email: data.email,
        password: data.password,
        name: data.name,
        role: data.role as Role,
        phoneNumber: data.phoneNumber,
        organizationId: organizationId, // Use hardcoded organization ID
        departmentId: parseInt(data.departmentId),
        designationId: parseInt(data.designationId),
        employeeId: data.employeeId || undefined,
        joiningDate: data.joiningDate ? data.joiningDate.toISOString().split('T')[0] : undefined,
        reportingManagerId: data.reportingManagerId || undefined,
      };

      await createUserMutation.mutateAsync(userData);

      // Show success toast and navigate back
      toast.current?.showSuccess('User created successfully');
      navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
    } catch (error) {
      toast.current?.showError('Failed to create user');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
  };

  // Get default values for form
  const getDefaultValues = () => {
    return {
      role: Role.ROLE_USER, // Default to regular user role
      organizationId: organizationId
    };
  };

  return (
    <div className="add-user p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add User"
        subtitle="Enter user details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={userFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
        />
      </Card>
    </div>
  );
};

export default AddUser;
