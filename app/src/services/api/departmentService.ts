/**
 * Service for handling department-related API operations
 * Integrated with backend department API endpoints
 */

import { apiClient } from './client';
import {
  DepartmentDTO,
  Department,
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
  LegacyCreateDepartmentRequest,
  LegacyUpdateDepartmentRequest,
} from '@/types/department.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

/**
 * Data transformation utilities
 */

/**
 * Transform form data to backend DepartmentDTO format
 */
function transformFormDataToDepartmentDTO(formData: any, organizationId: number): CreateDepartmentRequest {
  return {
    name: formData.name,
    description: formData.description || undefined,
    organizationId: organizationId,
  };
}

/**
 * Transform backend DepartmentDTO to frontend display format
 */
function transformDepartmentDTOToDisplayFormat(departmentDTO: DepartmentDTO): any {
  return {
    id: departmentDTO.id?.toString() || '',
    name: departmentDTO.name,
    description: departmentDTO.description,
    organizationId: departmentDTO.organizationId,
  };
}

/**
 * Transform form data to update request format
 */
function transformFormDataToUpdateRequest(formData: any, organizationId: number): UpdateDepartmentRequest {
  return {
    name: formData.name,
    description: formData.description || undefined,
    organizationId: organizationId,
  };
}



/**
 * Service class for department management operations
 */
export class DepartmentService {
  private static BASE_URL = '/v1/departments';

  /**
   * Get all departments or by organization ID
   * @param organizationId Optional organization ID to filter departments
   * @returns Promise with department data
   */
  static async getDepartments(organizationId?: number): Promise<DepartmentDTO[]> {
    try {
      const url = organizationId
        ? `${this.BASE_URL}?organizationId=${organizationId}`
        : this.BASE_URL;

      return await apiClient.get<DepartmentDTO[]>(url);
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  /**
   * Get a paginated list of departments (legacy method for backward compatibility)
   * @param params Query parameters for pagination, sorting, and filtering
   * @returns Promise with paginated department data
   */
  static async getDepartmentsPaginated(params?: QueryParams): Promise<PaginatedResponse<Department>> {
    try {
      // Get all departments first
      const organizationId = 40928446087168; // Use specified organization ID
      const departments = await this.getDepartments(organizationId);

      // Transform to legacy format for backward compatibility
      let transformedDepartments = departments.map(dept => ({
        id: dept.id?.toString() || '',
        name: dept.name,
        description: dept.description || null,
        organization_id: dept.organizationId.toString(),
        created_by_ip: '***********',
        created_by_user: '<EMAIL>',
        created_date: new Date().toISOString(),
        last_modified_by_ip: '***********',
        last_modified_by_user: '<EMAIL>',
        last_modified_date: new Date().toISOString(),
        version: 1,
        active: true
      }));

      // Apply sorting if provided
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        transformedDepartments.sort((a: any, b: any) => {
          const aValue = a[field] || '';
          const bValue = b[field] || '';

          // Handle string comparison properly
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
            return order === 'asc' ? comparison : -comparison;
          }

          // Handle other types
          if (order === 'asc') {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
          } else {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
          }
        });
      } else {
        // Default sorting by name
        transformedDepartments.sort((a, b) =>
          a.name.toLowerCase().localeCompare(b.name.toLowerCase())
        );
      }

      // Calculate pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // Get paginated data
      const paginatedData = transformedDepartments.slice(startIndex, endIndex);

      // Return paginated response
      return {
        data: paginatedData,
        total: transformedDepartments.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(transformedDepartments.length / limit)
      };
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  /**
   * Get a department by ID
   * @param id Department ID
   * @param organizationId Organization ID
   * @returns Promise with department data
   */
  static async getDepartmentById(id: number, organizationId: number): Promise<DepartmentDTO> {
    try {
      return await apiClient.get<DepartmentDTO>(`${this.BASE_URL}/${id}?organizationId=${organizationId}`);
    } catch (error) {
      console.error(`Error fetching department with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get a department by ID (legacy method for backward compatibility)
   * @param id Department ID as string
   * @returns Promise with department data in legacy format
   */
  static async getDepartmentByIdLegacy(id: string): Promise<ApiResponse<Department>> {
    try {
      const departmentId = parseInt(id);
      const department = await this.getDepartmentById(departmentId);

      // Transform to legacy format
      const legacyDepartment: Department = {
        id: department.id?.toString() || '',
        name: department.name,
        description: department.description || null,
        organization_id: department.organizationId.toString(),
        created_by_ip: '***********',
        created_by_user: '<EMAIL>',
        created_date: new Date().toISOString(),
        last_modified_by_ip: '***********',
        last_modified_by_user: '<EMAIL>',
        last_modified_date: new Date().toISOString(),
        version: 1,
        active: true
      };

      return {
        data: legacyDepartment,
        message: 'Department retrieved successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error fetching department with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search departments by name
   * @param name Department name to search for
   * @param organizationId Organization ID
   * @returns Promise with matching department data
   */
  static async searchDepartments(name: string, organizationId: number): Promise<DepartmentDTO[]> {
    try {
      return await apiClient.get<DepartmentDTO[]>(`${this.BASE_URL}/search?name=${name}&organizationId=${organizationId}`);
    } catch (error) {
      console.error(`Error searching departments by name "${name}" for organization ${organizationId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new department
   * @param data Department data (form data format)
   * @param organizationId Organization ID for the department
   * @returns Promise with created department data
   */
  static async createDepartment(data: any, organizationId: number = 40928446087168): Promise<DepartmentDTO> {
    try {
      const departmentData = transformFormDataToDepartmentDTO(data, organizationId);
      return await apiClient.post<DepartmentDTO>(this.BASE_URL, departmentData);
    } catch (error) {
      console.error('Error creating department:', error);
      throw error;
    }
  }

  /**
   * Create a new department (legacy method for backward compatibility)
   * @param data Department data in legacy format
   * @returns Promise with created department data
   */
  static async createDepartmentLegacy(data: LegacyCreateDepartmentRequest): Promise<ApiResponse<Department>> {
    try {
      // Transform legacy format to new format
      const organizationId = parseInt(data.organization_id || '40928446087168');
      const transformedData = {
        name: data.name,
        description: data.description || undefined, // Convert empty/null to undefined
      };

      const createdDepartment = await this.createDepartment(transformedData, organizationId);

      // Transform back to legacy format for response
      return {
        data: {
          id: createdDepartment.id?.toString() || '',
          name: createdDepartment.name,
          description: createdDepartment.description || null,
          organization_id: createdDepartment.organizationId.toString(),
          created_by_ip: '***********',
          created_by_user: '<EMAIL>',
          created_date: new Date().toISOString(),
          last_modified_by_ip: '***********',
          last_modified_by_user: '<EMAIL>',
          last_modified_date: new Date().toISOString(),
          version: 1,
          active: true
        },
        message: 'Department created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating department:', error);
      throw error;
    }
  }

  /**
   * Update an existing department
   * @param id Department ID
   * @param data Updated department data (form data format)
   * @param organizationId Organization ID for the department
   * @returns Promise with updated department data
   */
  static async updateDepartment(id: number, data: any, organizationId: number = 40928446087168): Promise<DepartmentDTO> {
    try {
      const departmentData = transformFormDataToUpdateRequest(data, organizationId);
      return await apiClient.put<DepartmentDTO>(`${this.BASE_URL}/${id}`, departmentData);
    } catch (error) {
      console.error(`Error updating department with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update an existing department (legacy method for backward compatibility)
   * @param id Department ID as string
   * @param data Updated department data in legacy format
   * @returns Promise with updated department data
   */
  static async updateDepartmentLegacy(id: string, data: LegacyUpdateDepartmentRequest): Promise<ApiResponse<Department>> {
    try {
      const departmentId = parseInt(id);
      const organizationId = parseInt(data.organization_id || '40928446087168');

      // Transform legacy format to new format
      const transformedData = {
        name: data.name,
        description: data.description || undefined, // Convert empty/null to undefined
      };

      const updatedDepartment = await this.updateDepartment(departmentId, transformedData, organizationId);

      // Transform back to legacy format for response
      return {
        data: {
          id: updatedDepartment.id?.toString() || '',
          name: updatedDepartment.name,
          description: updatedDepartment.description || null,
          organization_id: updatedDepartment.organizationId.toString(),
          created_by_ip: '***********',
          created_by_user: '<EMAIL>',
          created_date: new Date().toISOString(),
          last_modified_by_ip: '***********',
          last_modified_by_user: '<EMAIL>',
          last_modified_date: new Date().toISOString(),
          version: 1,
          active: true
        },
        message: 'Department updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating department with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a department by ID
   * @param id Department ID
   * @param organizationId Organization ID
   * @returns Promise indicating success or failure
   */
  static async deleteDepartment(id: number, organizationId: number): Promise<void> {
    try {
      await apiClient.delete(`${this.BASE_URL}/${id}?organizationId=${organizationId}`);
    } catch (error) {
      console.error(`Error deleting department with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a department (legacy method for backward compatibility)
   * @param id Department ID as string
   * @returns Promise with deletion confirmation
   */
  static async deleteDepartmentLegacy(id: string): Promise<ApiResponse<void>> {
    try {
      const departmentId = parseInt(id);
      await this.deleteDepartment(departmentId);

      return {
        data: undefined,
        message: 'Department deleted successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error deleting department with ID ${id}:`, error);
      throw error;
    }
  }
}
