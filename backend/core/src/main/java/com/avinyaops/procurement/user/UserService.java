package com.avinyaops.procurement.user;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface UserService {
    UserResponse createUser(UserCreateRequest request);

    UserResponse updateUser(Long id, UserUpdateRequest request);

    void deleteUser(Long id, Long organizationId);

    UserResponse getUser(Long id, Long organizationId);

    UserResponse enableUser(Long id, Long organizationId);

    UserResponse disableUser(Long id, Long organizationId);

    List<UserResponse> getAllUsers(Long organizationId);

    List<UserResponse> searchUsers(String searchTerm, Role role, Boolean enabled);

    boolean isCurrentUser(Long userId);
}