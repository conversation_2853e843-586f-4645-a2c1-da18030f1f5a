package com.avinyaops.procurement.organization.designation;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/designations")
@RequiredArgsConstructor
public class DesignationController {
    private final DesignationService designationService;

    @PostMapping
    public ResponseEntity<DesignationDTO> create(@RequestBody DesignationDTO designationDTO) {
        return ResponseEntity.ok(designationService.createDesignation(designationDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<DesignationDTO> update(@PathVariable Long id, @RequestBody DesignationDTO designationDTO) {
        return ResponseEntity.ok(designationService.updateDesignation(id, designationDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id, @RequestParam Long organizationId) {
        designationService.deleteDesignation(id, organizationId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    public ResponseEntity<DesignationDTO> get(@PathVariable Long id, @RequestParam Long organizationId) {
        return ResponseEntity.ok(designationService.getDesignation(id, organizationId));
    }

    @GetMapping
    public ResponseEntity<List<DesignationDTO>> getAll(@RequestParam Long organizationId) {
        return ResponseEntity.ok(designationService.getDesignationsByOrganization(organizationId));
    }
}